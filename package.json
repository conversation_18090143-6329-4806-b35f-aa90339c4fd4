{"name": "ragflow-ai-chat-workflow", "version": "1.0.0", "description": "n8n workflow for AI chat with RAGFlow-MCP integration", "main": "test-workflow.js", "scripts": {"test": "node test-workflow.js", "test:single": "node test-workflow.js", "install-deps": "npm install"}, "keywords": ["n8n", "workflow", "ai", "ragflow", "mcp", "chat", "retrieval"], "author": "AI Assistant", "license": "MIT", "dependencies": {"axios": "^1.6.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ragflow-ai-chat-workflow.git"}, "bugs": {"url": "https://github.com/your-username/ragflow-ai-chat-workflow/issues"}, "homepage": "https://github.com/your-username/ragflow-ai-chat-workflow#readme"}