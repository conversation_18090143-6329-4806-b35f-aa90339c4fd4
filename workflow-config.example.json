{"ragflow": {"baseUrl": "http://localhost:8080", "apiEndpoint": "/api/ragflow/retrieve", "defaultDatasetIds": ["bdb71c2472a911f0843842a951434676", "fc416ffa71ee11f0a70b42a951434676", "5f2a0bd071af11f0b1c942db62bd98d1"], "timeout": 30000}, "openai": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000, "systemMessage": "你是一个专业的AI助手，擅长基于提供的文档内容回答用户问题。请确保回答准确、简洁且有帮助。"}, "webhook": {"path": "ai-chat", "method": "POST", "responseMode": "responseNode"}, "prompts": {"userPromptTemplate": "基于以下检索到的内容，请回答用户的问题：\n\n用户问题：{{ question }}\n\n检索内容：\n{{ retrievedContent }}\n\n请根据检索到的内容提供准确、有用的回答。如果检索内容不足以回答问题，请说明这一点。", "noContentMessage": "未找到相关内容", "errorMessage": "抱歉，在处理您的问题时遇到了错误，请稍后重试。"}, "responses": {"success": {"statusCode": 200, "headers": {"Content-Type": "application/json"}}, "error": {"statusCode": 500, "headers": {"Content-Type": "application/json"}}}, "features": {"enableErrorHandling": true, "enableLogging": true, "enableCaching": false, "enableRateLimit": false}}