{"name": "RAGFlow AI Chat Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-chat", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [240, 300], "webhookId": "ai-chat-webhook"}, {"parameters": {"url": "http://localhost:8080/api/ragflow/retrieve", "method": "POST", "sendBody": true, "contentType": "json", "jsonBody": "={\n  \"question\": \"{{ $json.body.question }}\",\n  \"dataset_ids\": {{ $json.body.dataset_ids || [\"bdb71c2472a911f0843842a951434676\", \"fc416ffa71ee11f0a70b42a951434676\", \"5f2a0bd071af11f0b1c942db62bd98d1\"] }},\n  \"document_ids\": {{ $json.body.document_ids || [] }}\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "options": {"timeout": 30000}}, "id": "ragflow-retrieval", "name": "RAGFlow Retrieval", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "continueOnFail": true}, {"parameters": {"text": "=基于以下检索到的内容，请回答用户的问题：\n\n用户问题：{{ $('Webhook Trigger').item.json.body.question }}\n\n检索内容：\n{{ $json.chunks ? $json.chunks.map(chunk => `- ${chunk.content}`).join('\\n') : '未找到相关内容' }}\n\n请根据检索到的内容提供准确、有用的回答。如果检索内容不足以回答问题，请说明这一点。", "options": {"systemMessage": "你是一个专业的AI助手，擅长基于提供的文档内容回答用户问题。请确保回答准确、简洁且有帮助。"}}, "id": "ai-response", "name": "AI Response", "type": "n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [680, 300]}, {"parameters": {"model": {"value": "gpt-3.5-turbo", "mode": "list"}, "options": {"temperature": 0.7, "maxTokens": 1000}}, "id": "openai-model", "name": "OpenAI Model", "type": "n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [680, 480]}, {"parameters": {"respondWith": "json", "responseDataSource": "define", "responseJson": "={\n  \"success\": true,\n  \"question\": \"{{ $('Webhook Trigger').item.json.body.question }}\",\n  \"answer\": \"{{ $json.response || $json.text }}\",\n  \"retrieved_chunks\": {{ $('RAGFlow Retrieval').item.json.chunks ? $('RAGFlow Retrieval').item.json.chunks.length : 0 }},\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 200, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseDataSource": "define", "responseJson": "={\n  \"success\": false,\n  \"error\": \"Failed to retrieve information from RAGFlow\",\n  \"question\": \"{{ $('Webhook Trigger').item.json.body.question }}\",\n  \"message\": \"{{ $json.error?.message || 'Unknown error occurred' }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}", "options": {"responseCode": 500, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [900, 480]}], "connections": {"Webhook Trigger": {"main": [[{"node": "RAGFlow Retrieval", "type": "main", "index": 0}]]}, "RAGFlow Retrieval": {"main": [[], [{"node": "Error Response", "type": "main", "index": 0}]]}, "AI Response": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "OpenAI Model": {"main": [[{"node": "AI Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-08T10:00:00.000Z", "updatedAt": "2025-01-08T10:00:00.000Z", "id": "ai-chat", "name": "AI Chat"}], "triggerCount": 1, "updatedAt": "2025-01-08T10:00:00.000Z", "versionId": "1"}