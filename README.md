# RAGFlow AI Chat Workflow for n8n

这是一个完整的n8n工作流，实现了AI模型调用RAGFlow-MCP进行文档检索并生成智能回复的功能。

## 工作流概述

该工作流包含以下节点：

1. **Webhook Trigger** - 接收用户的聊天请求
2. **RAGFlow Retrieval** - 调用RAGFlow-MCP进行文档检索
3. **AI Response** - 使用AI模型基于检索内容生成回复
4. **OpenAI Model** - OpenAI聊天模型配置
5. **Success Response** - 成功时返回结果
6. **Error Response** - 错误时返回错误信息

## 工作流程

```
用户请求 → Webhook → RAGFlow检索 → AI生成回复 → 返回结果
                                ↓
                            错误处理 → 返回错误信息
```

## 安装和配置

### 1. 导入工作流

1. 在n8n中创建新工作流
2. 将 `ragflow-ai-chat-workflow.json` 的内容复制粘贴到n8n的JSON编辑器中
3. 保存工作流

### 2. 配置节点

#### Webhook Trigger 配置
- **HTTP Method**: POST
- **Path**: `ai-chat`
- **Response Mode**: Using 'Respond to Webhook' Node

#### RAGFlow Retrieval 配置
- **URL**: `http://localhost:8080/api/ragflow/retrieve` (根据实际RAGFlow-MCP地址修改)
- **Method**: POST
- **Content Type**: JSON
- **Body**: 包含question、dataset_ids、document_ids参数

#### OpenAI Model 配置
- **Model**: gpt-3.5-turbo (可根据需要修改)
- **Temperature**: 0.7
- **Max Tokens**: 1000
- **需要配置OpenAI API凭据**

### 3. 设置凭据

在n8n中配置以下凭据：
- **OpenAI API Key**: 用于AI模型调用

## API使用方法

### 请求格式

```bash
curl -X POST http://your-n8n-instance/webhook/ai-chat \
  -H "Content-Type: application/json" \
  -d '{
    "question": "用户的问题",
    "dataset_ids": ["bdb71c2472a911f0843842a951434676", "fc416ffa71ee11f0a70b42a951434676"],
    "document_ids": []
  }'
```

### 请求参数

- `question` (必需): 用户的问题
- `dataset_ids` (可选): RAGFlow数据集ID数组，默认使用预设的数据集
- `document_ids` (可选): 特定文档ID数组

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "question": "用户的问题",
  "answer": "AI生成的回答",
  "retrieved_chunks": 5,
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "Failed to retrieve information from RAGFlow",
  "question": "用户的问题",
  "message": "具体错误信息",
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

## 自定义配置

### 修改RAGFlow-MCP地址
在"RAGFlow Retrieval"节点中修改URL参数：
```
http://your-ragflow-mcp-host:port/api/ragflow/retrieve
```

### 修改AI模型
在"OpenAI Model"节点中可以选择不同的模型：
- gpt-3.5-turbo
- gpt-4
- gpt-4-turbo-preview

### 修改提示词
在"AI Response"节点中可以自定义系统提示词和用户提示词模板。

## 错误处理

工作流包含完整的错误处理机制：
- RAGFlow检索失败时会返回错误响应
- AI模型调用失败时会有相应的错误处理
- 所有错误都会记录时间戳和详细信息

## 扩展功能

可以考虑添加以下功能：
1. 用户会话管理
2. 检索结果缓存
3. 多轮对话支持
4. 结果评分和反馈
5. 日志记录和分析

## 注意事项

1. 确保RAGFlow-MCP服务正常运行
2. 配置正确的OpenAI API密钥
3. 根据实际需求调整数据集ID
4. 监控API调用频率和成本
5. 定期检查和更新工作流配置
