/**
 * RAGFlow AI Chat Workflow 测试脚本
 * 用于测试n8n工作流的功能
 */

const axios = require('axios');

// 配置
const config = {
  // n8n webhook URL - 请根据实际情况修改
  webhookUrl: 'http://localhost:5678/webhook/ai-chat',
  
  // 测试用例
  testCases: [
    {
      name: '基础问答测试',
      data: {
        question: '什么是人工智能？',
        dataset_ids: ['bdb71c2472a911f0843842a951434676', 'fc416ffa71ee11f0a70b42a951434676']
      }
    },
    {
      name: '技术问题测试',
      data: {
        question: '如何使用RAGFlow进行文档检索？',
        dataset_ids: ['bdb71c2472a911f0843842a951434676']
      }
    },
    {
      name: '空问题测试',
      data: {
        question: '',
        dataset_ids: ['bdb71c2472a911f0843842a951434676']
      }
    }
  ]
};

/**
 * 发送测试请求
 */
async function sendTestRequest(testCase) {
  try {
    console.log(`\n🧪 执行测试: ${testCase.name}`);
    console.log(`📝 问题: "${testCase.data.question}"`);
    
    const startTime = Date.now();
    
    const response = await axios.post(config.webhookUrl, testCase.data, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ 测试成功 (${duration}ms)`);
    console.log(`📊 状态码: ${response.status}`);
    console.log(`📋 响应数据:`, JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      testCase: testCase.name,
      duration,
      response: response.data
    };
    
  } catch (error) {
    console.log(`❌ 测试失败: ${testCase.name}`);
    
    if (error.response) {
      console.log(`📊 状态码: ${error.response.status}`);
      console.log(`📋 错误响应:`, JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log(`🔌 网络错误: 无法连接到 ${config.webhookUrl}`);
    } else {
      console.log(`⚠️ 请求错误: ${error.message}`);
    }
    
    return {
      success: false,
      testCase: testCase.name,
      error: error.message,
      response: error.response?.data
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始RAGFlow AI Chat工作流测试');
  console.log(`🔗 目标URL: ${config.webhookUrl}`);
  console.log(`📊 测试用例数量: ${config.testCases.length}`);
  
  const results = [];
  
  for (const testCase of config.testCases) {
    const result = await sendTestRequest(testCase);
    results.push(result);
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 输出测试总结
  console.log('\n📈 测试总结');
  console.log('='.repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const failCount = results.length - successCount;
  
  console.log(`✅ 成功: ${successCount}/${results.length}`);
  console.log(`❌ 失败: ${failCount}/${results.length}`);
  
  if (failCount > 0) {
    console.log('\n❌ 失败的测试:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.testCase}: ${r.error}`);
    });
  }
  
  if (successCount > 0) {
    console.log('\n✅ 成功的测试:');
    results.filter(r => r.success).forEach(r => {
      console.log(`  - ${r.testCase}: ${r.duration}ms`);
    });
  }
}

/**
 * 单个测试函数
 */
async function runSingleTest(question, datasetIds = null) {
  const testCase = {
    name: '自定义测试',
    data: {
      question,
      dataset_ids: datasetIds || ['bdb71c2472a911f0843842a951434676', 'fc416ffa71ee11f0a70b42a951434676']
    }
  };
  
  return await sendTestRequest(testCase);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 如果提供了参数，运行单个测试
    const question = args.join(' ');
    console.log('🧪 运行单个测试');
    await runSingleTest(question);
  } else {
    // 否则运行所有测试
    await runAllTests();
  }
}

// 导出函数供其他模块使用
module.exports = {
  runAllTests,
  runSingleTest,
  sendTestRequest
};

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}
